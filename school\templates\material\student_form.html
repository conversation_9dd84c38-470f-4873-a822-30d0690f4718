{% load i18n %}
{% load matter_forms %}

<div id="student-form">
    {% csrf_token %}
    
    <div class="student-form-columns">
        <!-- Column 1: Student Information -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">person</span>
                Informations sur l'élève
            </div>
            
            <!-- Student ID -->
             <div class="row">
                 <div class="form-field">
                     {% matter_field student_form.student_id class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                 </div>
             </div>
            <!-- Name Fields -->
            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% matter_field student_form.last_name class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                </div>
                <div class="form-field col-md-8">
                    {% if user.school.education != 'F' %}
                        {% matter_field student_form.first_name class="small" onkeyup="this.value = this.value.toUpperCase();" hx_get="/transliterate/?field=full_name_ar" hx_target="#id_0-full_name_ar" hx_trigger="keyup delay:3s" hx_swap="outerHTML" %}
                    {% else %}
                        {% matter_field student_form.first_name class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                    {% endif %}
                </div>
            </div>
            
            <!-- Birth Date Fields -->
            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% matter_field student_form.birth_day class="small" type="number" min="1" max="31" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.birth_month class="small" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.birth_year class="small" type="number" max="{{ max_year }}" %}
                </div>
            </div>
            
            <!-- Birth Place, Gender, Nationality -->
            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% if user.school.education != 'F' %}
                        {% matter_field student_form.birth_place class="small" onkeyup="this.value = this.value.toUpperCase();" hx_get="/transliterate/?field=birth_place_ar" hx_target="#id_0-birth_place_ar" hx_trigger="keyup delay:3s" hx_swap="outerHTML" %}
                    {% else %}
                        {% matter_field student_form.birth_place class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                    {% endif %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.gender class="small" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.nationality class="small" %}
                </div>
            </div>

            <!-- Arabic Name Fields (if not French education) -->
            {% if user.school.education != 'F' %}
            <div class="row gx-2">
                <div class="form-field col-md-6">
                    {% matter_field student_form.full_name_ar class='small' %}
                </div>
                <div class="form-field col-md-6">
                    {% matter_field student_form.birth_place_ar class='small' %}
                </div>
            </div>
            {% endif %}
        </div>
        
        <!-- Column 2: Parent Information and Photo -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">family_restroom</span>
                Infos parents et Photo
            </div>
            
            <!-- Father -->
            <div class="form-field">
                {% matter_field parents_form.father class='small' onkeyup="this.value = this.value.toUpperCase();" %}
            </div>

            <!-- Mother -->
            <div class="form-field">
                {% matter_field parents_form.mother class='small' onkeyup="this.value = this.value.toUpperCase();" %}
            </div>

            <!-- Parent Phone -->
            <div class="form-field">
                {% matter_field parents_form.father_phone class='small' type="tel" %}
            </div>
            
            <!-- Photo Upload Section -->
            <div class="photo-upload-section">
                {% if enrollment and enrollment.student.photo %}
                    <img alt="Photo de l'élève" class="photo-preview" 
                         src="{{ enrollment.student.photo.url }}" id="photo-preview">
                {% else %}
                    <img alt="Photo de l'élève" class="photo-preview" 
                         src="{{ blank_photo }}" id="photo-preview">
                {% endif %}
                
                <input type="file" class="mdc-text-field__input" 
                       name="3-photo" id="{{ files_form.photo.id_for_label }}"
                       accept=".jpg, .png, .jpeg" 
                       onchange="previewPhoto(this)"
                       style="display: none;">
                
                <button type="button" class="mdc-button mdc-button--outlined" 
                        onclick="document.getElementById('{{ files_form.photo.id_for_label }}').click()">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons mdc-button__icon">photo_camera</span>
                    <span class="mdc-button__label">Choisir une photo</span>
                </button>
            </div>
        </div>
        
        <!-- Column 3: Registration Information -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">school</span>
                Fréquentation {{ active_year }}
            </div>

            <!-- Subschool (if multiple) -->
            {% if user.school.subschool_set.count > 1 %}
            <div class="form-field">
                {% matter_field level_form.subschool class='small' %}
            </div>
            {% endif %}

            <!-- Level Fields -->
            <div class="row gx-2">
                {% if level_form.level_fr.help_text != 'd-none' %}
                <div class="form-field col-md-6">
                    {% matter_field level_form.level_fr class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                    {% matter_field level_form.generic_level_fr class='small d-none' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                </div>
                {% endif %}

                {% if level_form.level_ar.help_text != 'd-none' %}
                <div class="form-field col-md-6">
                    {% matter_field level_form.level_ar class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                    {% matter_field level_form.generic_level_ar class='small d-none' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                </div>
                {% endif %}
            </div>

            <!-- Quality and Status -->
            <div class="row gx-2">
                <div class="form-field col-md-6" style="flex: 2;">
                    {% matter_field level_form.qualite class='small' %}
                </div>
                <div class="form-field col-md-6" style="flex: 1;">
                    {% matter_field level_form.status class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                </div>
            </div>

            <!-- Fees Section -->
            {% if perms.school.add_payment %}
            <div class="form-section-title" style="margin-top: 24px;">
                <span class="material-icons">payments</span>
                Montant à payer par rubrique
            </div>

            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% matter_field level_form.enrollment_fees class='small' type="number" min="0" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field level_form.year_fees class='small' type="number" min="0" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field level_form.annexe_fees class='small' type="number" min="0" %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function previewPhoto(input) {
    const preview = document.getElementById('photo-preview');
    const reader = new FileReader();
    
    if (input.files && input.files[0]) {
        reader.onload = function(e) {
            preview.src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.src = '{{ blank_photo }}';
    }
}
</script>
