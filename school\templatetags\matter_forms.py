from django import template
from django.forms import <PERSON><PERSON><PERSON><PERSON>
from django.utils.html import format_html
from django.utils.safestring import mark_safe

register = template.Library()


@register.simple_tag
def matter_field(field, **kwargs):
    """
    Render a Django form field using Matter CSS styling.
    Supports text inputs, textareas, and select fields.
    """
    if not isinstance(field, BoundField):
        return ""
    
    # Extract attributes from kwargs
    css_class = kwargs.get('class', '')
    placeholder = kwargs.get('placeholder', field.label or '')
    
    # Build additional attributes
    attrs = {}
    for key, value in kwargs.items():
        if key not in ['class', 'placeholder']:
            attrs[key] = value
    
    # Determine field type and render accordingly
    widget_type = field.field.widget.__class__.__name__.lower()
    
    if 'select' in widget_type:
        return render_matter_select(field, css_class, attrs)
    elif 'textarea' in widget_type:
        return render_matter_textarea(field, css_class, placeholder, attrs)
    else:
        return render_matter_input(field, css_class, placeholder, attrs)


def render_matter_input(field, css_class, placeholder, attrs):
    """Render a Matter CSS text input field."""

    # Build attributes string
    attrs_str = ' '.join([f'{k}="{v}"' for k, v in attrs.items()])

    # Get field value
    value = field.value() if field.value() is not None else ''

    # Build required attribute
    required = 'required' if field.field.required else ''

    # Check for small variant
    small_class = 'small' if 'small' in css_class else ''

    # Determine input type
    input_type = attrs.get('type', 'text')
    if input_type == 'text':  # Only auto-detect if type not explicitly set
        if 'email' in field.name.lower():
            input_type = 'email'
        elif 'password' in field.name.lower():
            input_type = 'password'
        elif 'number' in field.name.lower() or 'age' in field.name.lower():
            input_type = 'number'
        elif 'date' in field.name.lower():
            input_type = 'date'

    html = f'''
    <div class="matter-textfield-outlined {small_class} {css_class}">
        <input
            type="{input_type}"
            name="{field.name}"
            id="{field.id_for_label}"
            value="{value}"
            placeholder=" "
            {required}
            {attrs_str}
        >
        <span>{field.label}</span>
    </div>
    '''
    
    # Add error messages if any
    if field.errors:
        html += '<div class="matter-field-errors">'
        for error in field.errors:
            html += f'<div class="matter-error-text">{error}</div>'
        html += '</div>'
    
    return mark_safe(html)


def render_matter_textarea(field, css_class, placeholder, attrs):
    """Render a Matter CSS textarea field."""

    # Build attributes string
    attrs_str = ' '.join([f'{k}="{v}"' for k, v in attrs.items()])

    # Get field value
    value = field.value() if field.value() is not None else ''

    # Build required attribute
    required = 'required' if field.field.required else ''

    # Check for small variant
    small_class = 'small' if 'small' in css_class else ''

    html = f'''
    <div class="matter-textfield-outlined {small_class} {css_class}">
        <textarea
            name="{field.name}"
            id="{field.id_for_label}"
            placeholder=" "
            {required}
            {attrs_str}
        >{value}</textarea>
        <span>{field.label}</span>
    </div>
    '''
    
    # Add error messages if any
    if field.errors:
        html += '<div class="matter-field-errors">'
        for error in field.errors:
            html += f'<div class="matter-error-text">{error}</div>'
        html += '</div>'
    
    return mark_safe(html)


def render_matter_select(field, css_class, attrs):
    """Render a Matter CSS select field."""

    # Build attributes string
    attrs_str = ' '.join([f'{k}="{v}"' for k, v in attrs.items()])

    # Get field value
    selected_value = field.value() if field.value() is not None else ''

    # Build required attribute
    required = 'required' if field.field.required else ''

    # Check for small variant
    small_class = 'small' if 'small' in css_class else ''

    # Get choices
    choices = field.field.choices

    # Generate unique wrapper ID
    wrapper_id = f"{field.id_for_label}_wrapper"

    html = f'''
    <div class="matter-select-wrapper {small_class} {css_class}" id="{wrapper_id}">
        <select
            name="{field.name}"
            id="{field.id_for_label}"
            class="matter-select"
            {required}
            {attrs_str}
        >
    '''
    
    # Add empty option if not required or no value selected
    if not field.field.required or not selected_value:
        html += '<option value="">------</option>'
    
    # Add options
    for value, label in choices:
        if value == '':  # Skip empty choice if already added
            continue
        selected_attr = 'selected' if str(value) == str(selected_value) else ''
        html += f'<option value="{value}" {selected_attr}>{label}</option>'
    
    html += f'''
        </select>
        <label class="matter-select-label">{field.label}</label>
    </div>
    '''
    
    # Add JavaScript for dropdown arrow animation
    html += f'''
    <script>
        (function() {{
            const wrapper = document.getElementById('{wrapper_id}');
            const select = document.getElementById('{field.id_for_label}');
            
            if (wrapper && select) {{
                let isOpen = false;
                
                // Handle select opening
                select.addEventListener('mousedown', function() {{
                    isOpen = !isOpen;
                    wrapper.setAttribute('data-open', isOpen);
                }});
                
                // Handle select closing
                select.addEventListener('blur', function() {{
                    isOpen = false;
                    wrapper.setAttribute('data-open', 'false');
                }});
                
                // Handle value changes to manage label floating
                select.addEventListener('change', function() {{
                    if (select.value) {{
                        select.classList.add('has-value');
                    }} else {{
                        select.classList.remove('has-value');
                    }}
                }});
                
                // Set initial state
                if (select.value) {{
                    select.classList.add('has-value');
                }}
            }}
        }})();
    </script>
    '''
    
    # Add error messages if any
    if field.errors:
        html += '<div class="matter-field-errors">'
        for error in field.errors:
            html += f'<div class="matter-error-text">{error}</div>'
        html += '</div>'
    
    return mark_safe(html)
