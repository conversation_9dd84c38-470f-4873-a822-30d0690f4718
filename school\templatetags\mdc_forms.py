"""
Material Design Components (MDC) Form Field Template Tags
Similar to widget_tweaks but specifically for Material Design components

Usage:
{% load mdc_forms %}
{% mdc_field form.field_name %}
{% mdc_field form.field_name class="additional-class" placeholder="Custom placeholder" %}
{% mdc_field form.field_name type="email" required=True %}
"""

from django import template
from django.forms import widgets
from django.utils.safestring import mark_safe
from django.utils.html import format_html
import re

register = template.Library()


def get_field_type(field):
    """Determine the appropriate input type for the field"""
    widget = field.field.widget

    # Check for fields with choices first
    if hasattr(field.field, 'choices') and field.field.choices:
        if isinstance(widget, widgets.RadioSelect):
            return 'radio'
        else:
            return 'select'

    if isinstance(widget, widgets.EmailInput):
        return 'email'
    elif isinstance(widget, widgets.PasswordInput):
        return 'password'
    elif isinstance(widget, widgets.NumberInput):
        return 'number'
    elif isinstance(widget, widgets.URLInput):
        return 'url'
    elif isinstance(widget, widgets.DateInput):
        return 'date'
    elif isinstance(widget, widgets.TimeInput):
        return 'time'
    elif isinstance(widget, widgets.DateTimeInput):
        return 'datetime-local'
    elif isinstance(widget, widgets.Textarea):
        return 'textarea'
    elif isinstance(widget, widgets.Select):
        return 'select'
    elif isinstance(widget, widgets.CheckboxInput):
        return 'checkbox'
    elif isinstance(widget, widgets.RadioSelect):
        return 'radio'
    else:
        return 'text'


def build_attrs(field, **kwargs):
    """Build HTML attributes for the field"""
    attrs = {
        'id': field.id_for_label,
        'name': field.name,
        'class': 'mdc-text-field__input'
    }
    
    # Add field value
    if field.value() is not None:
        attrs['value'] = field.value()
    
    # Add required attribute
    if field.field.required:
        attrs['required'] = True
    
    # Add field-specific attributes
    if hasattr(field.field, 'max_length') and field.field.max_length:
        attrs['maxlength'] = field.field.max_length
    
    if hasattr(field.field, 'min_length') and field.field.min_length:
        attrs['minlength'] = field.field.min_length
    
    # Add widget attributes
    if hasattr(field.field.widget, 'attrs'):
        attrs.update(field.field.widget.attrs)
    
    # Override with custom attributes
    attrs.update(kwargs)
    
    return attrs


def render_text_field(field, **kwargs):
    """Render Material Design text field"""
    field_type = kwargs.pop('type', get_field_type(field))
    extra_classes = kwargs.pop('class', '')
    placeholder = kwargs.pop('placeholder', field.label or '')

    # Extract HTMX and other special attributes
    special_attrs = {}
    for key in list(kwargs.keys()):
        if key.startswith('hx_') or key in ['onkeyup', 'onchange', 'onclick', 'onfocus', 'onblur']:
            # Convert hx_ to hx- for HTMX attributes
            attr_name = key.replace('hx_', 'hx-') if key.startswith('hx_') else key
            special_attrs[attr_name] = kwargs.pop(key)

    attrs = build_attrs(field, type=field_type, **kwargs)
    attrs.update(special_attrs)
    
    # Build CSS classes
    css_classes = ['mdc-text-field', 'mdc-text-field--outlined']
    if extra_classes:
        css_classes.extend(extra_classes.split())
    
    # Handle errors
    if field.errors:
        css_classes.append('mdc-text-field--invalid')
    
    # Build attributes string
    attr_str = ' '.join(f'{k}="{v}"' for k, v in attrs.items())
    
    html = f'''
    <div class="{' '.join(css_classes)}" id="{field.id_for_label}_container">
        <input {attr_str}>
        <div class="mdc-notched-outline">
            <div class="mdc-notched-outline__leading"></div>
            <div class="mdc-notched-outline__notch">
                <label class="mdc-floating-label" for="{field.id_for_label}">
                    {field.label}
                </label>
            </div>
            <div class="mdc-notched-outline__trailing"></div>
        </div>
    '''
    
    # Add helper text for errors
    if field.errors:
        html += f'''
        <div class="mdc-text-field-helper-line">
            <div class="mdc-text-field-helper-text mdc-text-field-helper-text--validation-msg">
                {field.errors[0]}
            </div>
        </div>
        '''
    
    html += '</div>'

    # Add auto-initialization script
    html += f'''
    <script>
        if (typeof initMDCField === 'undefined') {{
            window.initMDCField = function(containerId, componentType) {{
                const element = document.getElementById(containerId);
                if (!element || typeof mdc === 'undefined') return;

                switch(componentType) {{
                    case 'textField':
                        if (mdc.textField && !element.mdcTextField) {{
                            element.mdcTextField = new mdc.textField.MDCTextField(element);
                        }}
                        break;
                    case 'select':
                        if (mdc.select && !element.mdcSelect) {{
                            const selectComponent = new mdc.select.MDCSelect(element);
                            element.mdcSelect = selectComponent;

                            // Function to sync MDC select with hidden select
                            function syncHiddenSelect() {{
                                const hiddenSelect = element.querySelector('select');
                                if (hiddenSelect) {{
                                    const selectedValue = selectComponent.value;
                                    console.log('Syncing hidden select - Field:', hiddenSelect.name, 'MDC value:', selectedValue);

                                    // Update hidden select value
                                    hiddenSelect.value = selectedValue;

                                    // Update selected attribute on options
                                    Array.from(hiddenSelect.options).forEach(option => {{
                                        option.selected = (option.value === selectedValue);
                                    }});

                                    // Trigger change event
                                    hiddenSelect.dispatchEvent(new Event('change', {{ bubbles: true }}));

                                    console.log('Hidden select synced - Value:', hiddenSelect.value, 'Selected option:', hiddenSelect.selectedOptions[0]?.text);
                                }} else {{
                                    console.error('Hidden select not found in container:', element.id);
                                }}
                            }}

                            // Listen for MDC select changes
                            selectComponent.listen('MDCSelect:change', () => {{
                                console.log('MDC Select change event fired');
                                syncHiddenSelect();

                                // Trigger HTMX if needed
                                const anchor = element.querySelector('.mdc-select__anchor');
                                if (anchor && (anchor.hasAttribute('hx-get') || anchor.hasAttribute('hx-post'))) {{
                                    htmx.trigger(anchor, 'change');
                                }}
                            }});

                            // Set initial value from hidden select
                            setTimeout(() => {{
                                const hiddenSelect = element.querySelector('select');
                                if (hiddenSelect && hiddenSelect.value) {{
                                    selectComponent.value = hiddenSelect.value;
                                    console.log('Initial MDC select value set to:', hiddenSelect.value);
                                }}
                            }}, 100);
                        }}
                        break;
                    case 'checkbox':
                        const checkboxElement = element.querySelector('.mdc-checkbox');
                        if (mdc.checkbox && checkboxElement && !checkboxElement.mdcCheckbox) {{
                            checkboxElement.mdcCheckbox = new mdc.checkbox.MDCCheckbox(checkboxElement);
                        }}
                        if (mdc.formField && !element.mdcFormField) {{
                            element.mdcFormField = new mdc.formField.MDCFormField(element);
                            if (checkboxElement && checkboxElement.mdcCheckbox) {{
                                element.mdcFormField.input = checkboxElement.mdcCheckbox;
                            }}
                        }}
                        break;
                }}
            }};
        }}
        initMDCField('{field.id_for_label}_container', 'textField');
    </script>
    '''

    return mark_safe(html)


def render_textarea_field(field, **kwargs):
    """Render Material Design textarea field"""
    extra_classes = kwargs.pop('class', '')
    rows = kwargs.pop('rows', 4)
    
    attrs = build_attrs(field, **kwargs)
    attrs['class'] = 'mdc-text-field__input'
    
    # Build CSS classes
    css_classes = ['mdc-text-field', 'mdc-text-field--outlined', 'mdc-text-field--textarea']
    if extra_classes:
        css_classes.extend(extra_classes.split())
    
    # Handle errors
    if field.errors:
        css_classes.append('mdc-text-field--invalid')
    
    # Build attributes string
    attr_str = ' '.join(f'{k}="{v}"' for k, v in attrs.items() if k != 'value')
    
    html = f'''
    <div class="{' '.join(css_classes)}" id="{field.id_for_label}_container">
        <span class="mdc-notched-outline">
            <span class="mdc-notched-outline__leading"></span>
            <span class="mdc-notched-outline__notch">
                <span class="mdc-floating-label" for="{field.id_for_label}">
                    {field.label}
                </span>
            </span>
            <span class="mdc-notched-outline__trailing"></span>
        </span>
        <span class="mdc-text-field__resizer">
            <textarea {attr_str} rows="{rows}">{field.value() or ''}</textarea>
        </span>
    '''
    
    # Add helper text for errors
    if field.errors:
        html += f'''
        <div class="mdc-text-field-helper-line">
            <div class="mdc-text-field-helper-text mdc-text-field-helper-text--validation-msg">
                {field.errors[0]}
            </div>
        </div>
        '''
    
    html += '</div>'

    # Add auto-initialization script
    html += f'''
    <script>
        initMDCField('{field.id_for_label}_container', 'textField');
    </script>
    '''

    return mark_safe(html)


def render_select_field(field, **kwargs):
    """Render Material Design select field"""
    # Check if field actually has choices
    if not hasattr(field.field, 'choices'):
        # If no choices, render as text field instead
        return render_text_field(field, **kwargs)

    extra_classes = kwargs.pop('class', '')

    # Extract HTMX attributes
    htmx_attrs = {}
    for key in list(kwargs.keys()):
        if key.startswith('hx_'):
            # Convert hx_ to hx- for HTMX attributes
            attr_name = key.replace('hx_', 'hx-')
            htmx_attrs[attr_name] = kwargs.pop(key)

    # Build CSS classes
    css_classes = ['mdc-select', 'mdc-select--outlined']
    if extra_classes:
        css_classes.extend(extra_classes.split())

    # Handle errors
    if field.errors:
        css_classes.append('mdc-select--invalid')

    # Get choices
    choices = list(field.field.choices)
    selected_value = field.value()

    # Build HTMX attributes string
    htmx_attr_str = ' '.join(f'{k}="{v}"' for k, v in htmx_attrs.items())

    html = f'''
    <div class="{' '.join(css_classes)}" id="{field.id_for_label}_container">
        <div class="mdc-select__anchor" {htmx_attr_str}>
            <span class="mdc-notched-outline">
                <span class="mdc-notched-outline__leading"></span>
                <span class="mdc-notched-outline__notch">
                    <span class="mdc-floating-label">{field.label}</span>
                </span>
                <span class="mdc-notched-outline__trailing"></span>
            </span>
            <span class="mdc-select__selected-text-container">
                <span class="mdc-select__selected-text"></span>
            </span>
            <span class="mdc-select__dropdown-icon">
                <span class="material-icons">arrow_drop_down</span>
            </span>
        </div>
        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
            <ul class="mdc-deprecated-list">
    '''
    
    # Add choices
    for value, label in choices:
        selected_attr = 'aria-selected="true"' if str(value) == str(selected_value) else ''
        selected_class = 'mdc-deprecated-list-item--selected' if str(value) == str(selected_value) else ''
        html += f'''
                <li class="mdc-deprecated-list-item {selected_class}" data-value="{value}" {selected_attr}>
                    <span class="mdc-deprecated-list-item__text">{label}</span>
                </li>
        '''
    
    # Build required attribute if field is required
    required_attr = 'required' if field.field.required else ''

    html += '''
            </ul>
        </div>
        <select name="{}" id="{}" style="display: none;" {}>
    '''.format(field.name, field.id_for_label, required_attr)
    
    # Add hidden select options
    for value, label in choices:
        selected_attr = 'selected' if str(value) == str(selected_value) else ''
        html += f'<option value="{value}" {selected_attr}>{label}</option>'
    
    html += '</select>'
    
    # Add helper text for errors
    if field.errors:
        html += f'''
        <div class="mdc-text-field-helper-line">
            <div class="mdc-text-field-helper-text mdc-text-field-helper-text--validation-msg">
                {field.errors[0]}
            </div>
        </div>
        '''
    
    html += '</div>'

    # Add auto-initialization script
    html += f'''
    <script>
        initMDCField('{field.id_for_label}_container', 'select');

        // Ensure form submission includes select values
        document.addEventListener('DOMContentLoaded', function() {{
            const container = document.getElementById('{field.id_for_label}_container');
            if (container) {{
                const form = container.closest('form');
                if (form) {{
                    form.addEventListener('submit', function(e) {{
                        const mdcSelect = container.mdcSelect;
                        const hiddenSelect = container.querySelector('select');
                        if (mdcSelect && hiddenSelect) {{
                            const mdcValue = mdcSelect.value;
                            hiddenSelect.value = mdcValue;

                            // Update selected attribute
                            Array.from(hiddenSelect.options).forEach(option => {{
                                option.selected = (option.value === mdcValue);
                            }});

                            console.log('Form submit - Ensuring field', hiddenSelect.name, 'has value:', hiddenSelect.value);
                        }}
                    }});
                }}
            }}
        }});
    </script>
    '''

    return mark_safe(html)


def render_checkbox_field(field, **kwargs):
    """Render Material Design checkbox field"""
    extra_classes = kwargs.pop('class', '')
    
    attrs = build_attrs(field, type='checkbox', **kwargs)
    attrs['class'] = 'mdc-checkbox__native-control'
    
    # Check if field is checked
    if field.value():
        attrs['checked'] = True
    
    # Build CSS classes
    css_classes = ['mdc-checkbox']
    if extra_classes:
        css_classes.extend(extra_classes.split())
    
    # Build attributes string
    attr_str = ' '.join(f'{k}="{v}"' if v is not True else k for k, v in attrs.items())
    
    html = f'''
    <div class="mdc-form-field" id="{field.id_for_label}_container">
        <div class="{' '.join(css_classes)}">
            <input {attr_str}>
            <div class="mdc-checkbox__background">
                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                </svg>
                <div class="mdc-checkbox__mixedmark"></div>
            </div>
            <div class="mdc-checkbox__ripple"></div>
        </div>
        <label for="{field.id_for_label}">{field.label}</label>
    </div>
    <script>
        initMDCField('{field.id_for_label}_container', 'checkbox');
    </script>
    '''

    return mark_safe(html)


@register.simple_tag
def mdc_field(field, **kwargs):
    """
    Render a Django form field as a Material Design component
    
    Usage:
    {% mdc_field form.field_name %}
    {% mdc_field form.field_name class="extra-class" placeholder="Custom placeholder" %}
    {% mdc_field form.field_name type="email" required=True %}
    """
    field_type = get_field_type(field)
    
    if field_type == 'textarea':
        return render_textarea_field(field, **kwargs)
    elif field_type == 'select':
        return render_select_field(field, **kwargs)
    elif field_type == 'checkbox':
        return render_checkbox_field(field, **kwargs)
    else:
        return render_text_field(field, **kwargs)


@register.simple_tag
def mdc_text_field(field, **kwargs):
    """Render specifically as text field"""
    return render_text_field(field, **kwargs)


@register.simple_tag
def mdc_select_field(field, **kwargs):
    """Render specifically as select field"""
    return render_select_field(field, **kwargs)


@register.simple_tag
def mdc_textarea_field(field, **kwargs):
    """Render specifically as textarea field"""
    return render_textarea_field(field, **kwargs)


@register.simple_tag
def mdc_checkbox_field(field, **kwargs):
    """Render specifically as checkbox field"""
    return render_checkbox_field(field, **kwargs)
