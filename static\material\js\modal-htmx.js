/**
 * Material Design Modal HTMX Integration
 * Provides automatic modal show/hide behavior similar to the old Bootstrap modal system
 * 
 * Features:
 * - Automatically shows modal when HTMX targets modal content
 * - Automatically hides modal on 204 status responses
 * - Initializes Material Design components after content swap
 * - Handles modal cleanup on hide
 */

(function() {
    'use strict';
    
    // Modal instances cache
    const modalInstances = new Map();
    
    /**
     * Get or create modal instance (always refresh to handle DOM changes)
     */
    function getModalInstance(modalId) {
        // Always refresh the modal instance to handle DOM changes after HTMX requests
        const modalOverlay = document.getElementById(modalId + '-overlay');
        if (modalOverlay) {
            const instance = {
                overlay: modalOverlay,
                modal: modalOverlay.querySelector('.modal'),
                content: modalOverlay.querySelector('.modal-content, [id$="-content"]'),
                title: modalOverlay.querySelector('.modal-title'),
                closeBtn: modalOverlay.querySelector('.modal-close')
            };
            modalInstances.set(modalId, instance);
            return instance;
        }
        return modalInstances.get(modalId);
    }
    
    /**
     * Clear modal instances cache (useful after DOM changes)
     */
    function clearModalInstancesCache() {
        modalInstances.clear();
    }

    /**
     * Show modal with animation
     */
    function showModal(modalId) {
        const instance = getModalInstance(modalId);
        if (!instance) {
            console.warn(`Modal instance not found for ${modalId}`);
            return;
        }

        instance.overlay.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Focus first input after animation
        setTimeout(() => {
            const firstInput = instance.content.querySelector('input, select, textarea');
            if (firstInput) {
                firstInput.focus();
            }
        }, 300);

        console.log(`Material modal ${modalId} shown`);
    }
    
    /**
     * Hide modal with animation
     */
    function hideModal(modalId) {
        const instance = getModalInstance(modalId);
        if (!instance) return;
        
        instance.overlay.classList.remove('active');
        document.body.style.overflow = '';
        
        console.log(`Material modal ${modalId} hidden`);
    }
    
    /**
     * Clear modal content
     */
    function clearModalContent(modalId) {
        const instance = getModalInstance(modalId);
        if (!instance || !instance.content) return;
        
        // Clear content after hide animation completes
        setTimeout(() => {
            instance.content.innerHTML = '';
        }, 300);
    }
    
    /**
     * Initialize Material Design components in modal content
     */
    function initializeMaterialComponents(container) {
        if (!container || typeof mdc === 'undefined') return;
        
        // Initialize text fields
        container.querySelectorAll('.mdc-text-field').forEach(textField => {
            if (!textField.mdcTextField) {
                try {
                    textField.mdcTextField = new mdc.textField.MDCTextField(textField);
                } catch (e) {
                    console.warn('Failed to initialize MDC text field:', e);
                }
            }
        });
        
        // Initialize select fields
        container.querySelectorAll('.mdc-select').forEach(select => {
            if (!select.mdcSelect) {
                try {
                    const selectComponent = new mdc.select.MDCSelect(select);
                    select.mdcSelect = selectComponent;
                    
                    // Handle HTMX triggers on select change
                    selectComponent.listen('MDCSelect:change', () => {
                        const anchor = select.querySelector('.mdc-select__anchor');
                        if (anchor && anchor.hasAttribute('hx-get')) {
                            htmx.trigger(anchor, 'change');
                        }
                    });
                } catch (e) {
                    console.warn('Failed to initialize MDC select:', e);
                }
            }
        });
        
        // Initialize checkboxes
        container.querySelectorAll('.mdc-checkbox').forEach(checkbox => {
            if (!checkbox.mdcCheckbox) {
                try {
                    checkbox.mdcCheckbox = new mdc.checkbox.MDCCheckbox(checkbox);
                } catch (e) {
                    console.warn('Failed to initialize MDC checkbox:', e);
                }
            }
        });
        
        // Initialize radio buttons
        container.querySelectorAll('.mdc-radio').forEach(radio => {
            if (!radio.mdcRadio) {
                try {
                    radio.mdcRadio = new mdc.radio.MDCRadio(radio);
                } catch (e) {
                    console.warn('Failed to initialize MDC radio:', e);
                }
            }
        });
        
        // Initialize switches
        container.querySelectorAll('.mdc-switch').forEach(switchEl => {
            if (!switchEl.mdcSwitch) {
                try {
                    switchEl.mdcSwitch = new mdc.switchControl.MDCSwitch(switchEl);
                } catch (e) {
                    console.warn('Failed to initialize MDC switch:', e);
                }
            }
        });
        
        // Initialize data tables
        container.querySelectorAll('.mdc-data-table').forEach(dataTable => {
            if (!dataTable.mdcDataTable) {
                try {
                    dataTable.mdcDataTable = new mdc.dataTable.MDCDataTable(dataTable);
                } catch (e) {
                    console.warn('Failed to initialize MDC data table:', e);
                }
            }
        });
    }
    
    /**
     * Get modal ID from target element
     */
    function getModalIdFromTarget(targetId) {
        // Handle different modal target patterns
        if (targetId.endsWith('-content')) {
            return targetId.replace('-content', '');
        }
        if (targetId === 'dialog') {
            return 'modal';
        }
        if (targetId === 'dialog-xl') {
            return 'modal-xl';
        }
        return targetId;
    }
    
    // HTMX Event Handlers
    
    /**
     * Handle HTMX afterSwap - show modal when content is swapped
     */
    document.addEventListener('htmx:afterSwap', function(event) {
        const target = event.detail.target;
        if (!target) return;

        const targetId = target.id;
        const modalId = getModalIdFromTarget(targetId);

        console.log(`HTMX afterSwap: target=${targetId}, modalId=${modalId}`);

        // Check if target is a modal content area
        if (targetId.includes('modal') || targetId === 'dialog' || targetId === 'dialog-xl') {
            // Clear cache to ensure fresh modal instance
            clearModalInstancesCache();

            // Initialize Material components in the new content
            initializeMaterialComponents(target);

            // Show the modal with a small delay to ensure DOM is ready
            setTimeout(() => {
                showModal(modalId);
            }, 50);
        }
    });
    
    /**
     * Handle HTMX beforeSwap - hide modal on empty response or 204 status
     */
    document.addEventListener('htmx:beforeSwap', function(event) {
        const target = event.detail.target;
        const xhr = event.detail.xhr;
        
        if (!target) return;
        
        const targetId = target.id;
        const modalId = getModalIdFromTarget(targetId);
        
        // Handle modal targets
        if (targetId.includes('modal') || targetId === 'dialog' || targetId === 'dialog-xl') {
            const statusCode = xhr.status;
            
            // Hide modal on 204 (No Content) or 201 (Created) responses
            if (statusCode === 204 || statusCode === 201) {
                hideModal(modalId);
                clearModalContent(modalId);
                event.detail.shouldSwap = false;
                
                // Show success message for 204
                if (statusCode === 204) {
                    // You can integrate with your notification system here
                    console.log('Action completed successfully');
                }
            }
            
            // Hide modal on empty response
            if (!xhr.response || xhr.response.trim() === '') {
                hideModal(modalId);
                clearModalContent(modalId);
                event.detail.shouldSwap = false;
            }
        }
    });
    
    /**
     * Initialize modal close handlers
     */
            // Handle modal close buttons
        document.addEventListener('click', function(event) {
            const closeBtn = event.target.closest('.modal-close, .modal-cancel');
            if (closeBtn) {
                const modal = closeBtn.closest('.modal');
                if (modal) {
                    const modalId = modal.id;
                    hideModal(modalId);
                    clearModalContent(modalId);
                }
            }
        });
        
        // Handle overlay clicks
        document.addEventListener('click', function(event) {
            if (event.target.matches('.modal-overlay')) {
                const modalId = event.target.id.replace('-overlay', '');
                hideModal(modalId);
                clearModalContent(modalId);
            }
        });

    // Clear modal cache when page content is updated
    document.addEventListener('htmx:beforeSwap', function(event) {
        // Clear cache before any content swap to ensure fresh modal instances
        clearModalInstancesCache();
    });

    // Expose functions globally for external use
    window.MaterialModalHTMX = {
        show: showModal,
        hide: hideModal,
        clear: clearModalContent,
        clearCache: clearModalInstancesCache,
        initializeComponents: initializeMaterialComponents
    };
    
})();
