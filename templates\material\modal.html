{% comment %}
Material Design M2 Reusable Modal Component
Compatible with HTMX and Django forms

Usage:
{% include 'material/modal.html' with modal_id='student-modal' modal_title='Add Student' %}

Parameters:
- modal_id: Unique ID for the modal (required)
- modal_title: Title displayed in modal header (required)
- modal_size: 'small', 'medium', 'large', 'xl' (default: 'medium')
- modal_form_id: ID for the form inside modal (default: modal_id + '-form')
- submit_text: Text for submit button (default: 'Save')
- cancel_text: Text for cancel button (default: 'Cancel')
- show_footer: Whether to show modal footer with buttons (default: true)
{% endcomment %}

{% load static %}

<!-- Modal Overlay -->
<div class="modal-overlay" id="{{ modal_id }}-overlay">
    <form action="{{ request.path }}" 
        class="modal {% if modal_size == 'small' %}modal--small{% elif modal_size == 'large' %}modal--large{% elif modal_size == 'xl' %}modal--xl{% endif %}" 
        id="{{ modal_id }}" 
        enctype="multipart/form-data"
        hx-post="{{ request.path }}"
        hx-target="#{{ modal_id }}-content">
        {% csrf_token %}
        <!-- Modal Header -->
        <div class="modal-header">
            <h2 class="modal-title" id="{{ modal_id }}-title">{{ modal_title|default:"Modal" }}</h2>
            <button class="modal-close" id="{{ modal_id }}-close" type="button">
                <span class="material-icons">close</span>
            </button>
        </div>
        
        <!-- Modal Content -->
        <div class="modal-content" id="{{ modal_id }}-content">
            <!-- Content will be loaded here via HTMX or populated directly -->
        </div>
        
        <!-- Modal Footer -->
        {% if not hide_footer %}
        <div class="modal-actions" id="{{ modal_id }}-actions">
            <button type="button" class="mdc-button mdc-button--outlined modal-cancel" id="{{ modal_id }}-cancel">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">{{ cancel_text|default:"Annuler" }}</span>
            </button>
            <button type="submit" class="mdc-button mdc-button--raised modal-submit" id="{{ modal_id }}-submit">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">{{ submit_text|default:"Enregistrer" }}</span>
            </button>
        </div>
        {% endif %}
    </form>
</div>

<script>
// Initialize Material Design components for {{ modal_id }} modal
(function() {
    const modalId = '{{ modal_id }}';
    const modalCancel = document.getElementById(modalId + '-cancel');
    const modalSubmit = document.getElementById(modalId + '-submit');
    const modalClose = document.getElementById(modalId + '-close');
    const modalOverlay = document.getElementById(modalId + '-overlay');

    // Initialize MDC ripple for modal buttons
    function initializeModalButtons() {
        if (typeof mdc !== 'undefined' && mdc.ripple) {
            if (modalCancel && !modalCancel.mdcRipple) {
                modalCancel.mdcRipple = new mdc.ripple.MDCRipple(modalCancel);
            }
            if (modalSubmit && !modalSubmit.mdcRipple) {
                modalSubmit.mdcRipple = new mdc.ripple.MDCRipple(modalSubmit);
            }
        }
    }
    // Hide modal
    // function hideModal() {
    //     if (modalOverlay) {
    //         modalOverlay.classList.remove('active');
    //         document.body.style.overflow = '';
    //     }
    // }

    // modalCancel.addEventListener('click', hideModal);
    // modalClose.addEventListener('click', hideModal)

    // Initialize on load
    document.addEventListener('DOMContentLoaded', initializeModalButtons);

    // Handle escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const modalOverlay = document.getElementById(modalId + '-overlay');
            if (modalOverlay && modalOverlay.classList.contains('active')) {
                window.MaterialModalHTMX.hide(modalId);
                window.MaterialModalHTMX.clear(modalId);
            }
        }
    });

    // Expose modal control functions globally
    window[modalId + 'Modal'] = {
        show: () => window.MaterialModalHTMX.show(modalId),
        hide: () => {
            window.MaterialModalHTMX.hide(modalId);
            window.MaterialModalHTMX.clear(modalId);
        },
        setTitle: (title) => {
            const modalTitle = document.getElementById(modalId + '-title');
            if (modalTitle) modalTitle.textContent = title;
        }
    };
})();
</script>
